'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/use-toast";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Loader2, Clock } from "lucide-react";
import { useSimpleStaff } from '@/lib/hooks/use-new-staff';
import { staffService } from '@/lib/services/staff-service';

// Form validation schema - simplified without permissions
const staffFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  role: z.string(),
  email: z.string().email("Invalid email").optional().or(z.literal('')),
  phone: z.string().optional(),
  status: z.enum(["ACTIVE", "INACTIVE"]).default("ACTIVE"),
  paymentConfig: z.object({
    type: z.enum(["MONTHLY", "WEEKLY", "PER_SHIFT", "DAILY"]).default("MONTHLY"),
    baseSalary: z.coerce.number().default(0),
    shiftRate: z.coerce.number().optional(),
    shiftRates: z.record(z.string(), z.coerce.number()).optional(),
  }),
  weeklySchedule: z.object({
    monday: z.array(z.string()).default([]),
    tuesday: z.array(z.string()).default([]),
    wednesday: z.array(z.string()).default([]),
    thursday: z.array(z.string()).default([]),
    friday: z.array(z.string()).default([]),
    saturday: z.array(z.string()).default([]),
    sunday: z.array(z.string()).default([]),
  }),
});

type StaffFormValues = z.infer<typeof staffFormSchema>;

interface SimpleStaffFormProps {
  onSuccess?: (staffId: string) => void;
  onCancel?: () => void;
  initialData?: {
    id: string;
    name: string;
    role: string;
    email?: string;
    phone?: string;
    status?: 'ACTIVE' | 'INACTIVE';
    paymentConfig?: {
      type?: 'MONTHLY' | 'WEEKLY' | 'PER_SHIFT' | 'DAILY';
      baseSalary?: number;
      shiftRate?: number;
      shiftRates?: Record<string, number>;
    };
    weeklySchedule?: Record<string, string[]>;
  };
}

export function SimpleStaffForm({ onSuccess, onCancel, initialData }: SimpleStaffFormProps) {
  const { toast } = useToast();
  const { createStaff, updateStaff } = useSimpleStaff();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [shifts, setShifts] = useState<any[]>([]);

  // Load shifts
  useEffect(() => {
    const loadShifts = async () => {
      try {
        const shiftData = await staffService.getAllShifts();
        setShifts(shiftData);
      } catch (error) {
        console.error('Error loading shifts:', error);
        setShifts([]);
      }
    };
    loadShifts();
  }, []);

  // Initialize form with default or initialData values
  const form = useForm<StaffFormValues>({
    resolver: zodResolver(staffFormSchema),
    defaultValues: {
      name: initialData?.name || '',
      role: initialData?.role || 'staff',
      email: initialData?.email || '',
      phone: initialData?.phone || '',
      status: initialData?.status || 'ACTIVE',
      paymentConfig: {
        type: initialData?.paymentConfig?.type || 'MONTHLY',
        baseSalary: initialData?.paymentConfig?.baseSalary ?? 0,
        shiftRate: initialData?.paymentConfig?.shiftRate,
        shiftRates: initialData?.paymentConfig?.shiftRates || {},
      },
      weeklySchedule: initialData?.weeklySchedule || {
        monday: [],
        tuesday: [],
        wednesday: [],
        thursday: [],
        friday: [],
        saturday: [],
        sunday: [],
      },
    },
  });

  // Watch for payment type changes and update shift rates accordingly
  const paymentType = form.watch("paymentConfig.type");
  const defaultShiftRate = form.watch("paymentConfig.shiftRate");
  const shiftRates = form.watch("paymentConfig.shiftRates");

  // Initialize shift rates when shifts are loaded or payment type changes
  useEffect(() => {
    if (shifts.length > 0) {
      const currentPaymentType = form.getValues("paymentConfig.type");
      if (currentPaymentType === "PER_SHIFT") {
        const currentShiftRates = form.getValues("paymentConfig.shiftRates") || {};
        const currentDefaultRate = form.getValues("paymentConfig.shiftRate") || 0;
        let needsUpdate = false;

        const updatedShiftRates = { ...currentShiftRates };
        shifts.forEach(shift => {
          if (!updatedShiftRates[shift.id]) {
            updatedShiftRates[shift.id] = currentDefaultRate;
            needsUpdate = true;
          }
        });

        if (needsUpdate) {
          form.setValue("paymentConfig.shiftRates", updatedShiftRates);
        }
      }
    }
  }, [shifts, form]);

  useEffect(() => {
    if (paymentType === "PER_SHIFT" && shifts.length > 0) {
      const currentShiftRates = form.getValues("paymentConfig.shiftRates") || {};
      let needsUpdate = false;

      const updatedShiftRates = { ...currentShiftRates };
      shifts.forEach(shift => {
        if (!updatedShiftRates[shift.id]) {
          updatedShiftRates[shift.id] = defaultShiftRate || 0;
          needsUpdate = true;
        }
      });

      if (needsUpdate) {
        form.setValue("paymentConfig.shiftRates", updatedShiftRates);
      }
    }
  }, [paymentType, defaultShiftRate, shifts, form]);

  // Handle shift toggle for weekly schedule
  const handleShiftToggle = (day: keyof StaffFormValues['weeklySchedule'], shiftId: string) => {
    const currentSchedule = form.getValues('weeklySchedule');
    const dayShifts = currentSchedule[day];
    
    if (dayShifts.includes(shiftId)) {
      // Remove shift
      form.setValue(`weeklySchedule.${day}`, dayShifts.filter(id => id !== shiftId));
    } else {
      // Add shift
      form.setValue(`weeklySchedule.${day}`, [...dayShifts, shiftId]);
    }
  };

  // Handle form submission
  const onSubmit = async (data: StaffFormValues) => {
    setIsSubmitting(true);
    try {
      if (initialData) {
        // Update existing staff member - separate staff data from schedule data
        const staffUpdatePayload = {
          name: data.name,
          role: data.role,
          email: data.email,
          phone: data.phone,
          status: data.status,
          paymentConfig: {
            type: data.paymentConfig.type,
            baseSalary: data.paymentConfig.baseSalary,
            shiftRate: data.paymentConfig.shiftRate,
            shiftRates: data.paymentConfig.shiftRates,
          },
          // DON'T include schedule here - it needs to be handled separately
        };
        
        // Update staff data first (without schedule)
        const updated = await updateStaff(initialData.id, staffUpdatePayload as any);
        
        // Then handle schedule separately using the proper per-staff schedule operations
        try {
          const { setStaffSchedule } = await import('@/lib/db/v4');
          await setStaffSchedule(initialData.id, {
            weeklySchedule: data.weeklySchedule,
            effectiveFrom: new Date().toISOString(),
            isActive: true,
          });
          console.log('✅ Schedule updated separately for staff:', initialData.id);
        } catch (scheduleError) {
          console.warn('⚠️ Failed to update schedule, but staff data was updated:', scheduleError);
          // Don't fail the entire operation if schedule update fails
        }
        
        toast({ title: "Staff updated", description: `${data.name} has been updated` });
        if (onSuccess) onSuccess(updated.id);
      } else {
        // Create new staff member
        const createPayload = {
          name: data.name,
          role: data.role,
          email: data.email,
          phone: data.phone,
          status: data.status,
          paymentConfig: {
            type: data.paymentConfig.type,
            baseSalary: data.paymentConfig.baseSalary,
            shiftRate: data.paymentConfig.shiftRate,
            shiftRates: data.paymentConfig.shiftRates,
          },
          // For new staff, we can include weeklySchedule as it's handled in the creation logic
          weeklySchedule: data.weeklySchedule,
        };
        const staffMember = await createStaff(createPayload as any);
        
        // For new staff, also set up the schedule separately to ensure consistency
        try {
          const { setStaffSchedule } = await import('@/lib/db/v4');
          await setStaffSchedule(staffMember.id, {
            weeklySchedule: data.weeklySchedule,
            effectiveFrom: new Date().toISOString(),
            isActive: true,
          });
          console.log('✅ Schedule created separately for new staff:', staffMember.id);
        } catch (scheduleError) {
          console.warn('⚠️ Failed to create separate schedule document, but staff was created:', scheduleError);
          // Don't fail the entire operation if schedule creation fails
        }
        
        // Show success message
        toast({ title: "Staff created", description: `${data.name} has been added to the staff list` });
        // Reset form and call callback
        form.reset();
        if (onSuccess) onSuccess(staffMember.id);
      }
    } catch (error) {
      console.error('Error creating/updating staff:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create/update staff",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const watchedSchedule = form.watch('weeklySchedule');

  return (
    <div className="space-y-6 p-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information - Compact Grid */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Staff name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="staff">Staff</SelectItem>
                        <SelectItem value="manager">Manager</SelectItem>
                        <SelectItem value="WAITER">Waiter</SelectItem>
                        <SelectItem value="CHEF">Chef</SelectItem>
                        <SelectItem value="CASHIER">Cashier</SelectItem>
                        <SelectItem value="DELIVERY">Delivery</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="ACTIVE">Active</SelectItem>
                        <SelectItem value="INACTIVE">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="Email address" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="Phone number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Payment Configuration - Compact */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Payment Configuration</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="paymentConfig.type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Type</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="MONTHLY">Monthly</SelectItem>
                        <SelectItem value="WEEKLY">Weekly</SelectItem>
                        <SelectItem value="DAILY">Daily</SelectItem>
                        <SelectItem value="PER_SHIFT">Per Shift</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="paymentConfig.baseSalary"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Base Salary</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="0" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {form.watch("paymentConfig.type") === "PER_SHIFT" && (
                <>
                  <FormField
                    control={form.control}
                    name="paymentConfig.shiftRate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Default Shift Rate</FormLabel>
                        <FormControl>
                          <Input type="number" placeholder="0" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="col-span-2 space-y-3">
                  <div className="flex items-center justify-between">
                    <FormLabel className="text-sm font-medium">Individual Shift Rates</FormLabel>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="h-7 text-xs px-2"
                      onClick={() => {
                        // Apply default rate to all shifts
                        const currentShiftRates = form.getValues("paymentConfig.shiftRates") || {};
                        const updatedShiftRates = { ...currentShiftRates };
                        shifts.forEach(shift => {
                          updatedShiftRates[shift.id] = defaultShiftRate || 0;
                        });
                        form.setValue("paymentConfig.shiftRates", updatedShiftRates);
                      }}
                    >
                      Apply Default ({defaultShiftRate || 0})
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
                    {shifts.map((shift: any) => (
                      <div key={shift.id} className="flex items-center justify-between p-2 border rounded">
                        <div className="flex-1">
                          <div className="font-medium text-sm">{shift.name}</div>
                          <div className="text-xs text-muted-foreground">
                            {shift.startTime} - {shift.endTime}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="relative">
                            <span className="absolute left-2 top-1/2 -translate-y-1/2 text-xs text-muted-foreground">DZD</span>
                            <Input
                              type="number"
                              min="0"
                              step="50"
                              value={shiftRates?.[shift.id] || 0}
                              onChange={(e) => {
                                const currentShiftRates = form.getValues("paymentConfig.shiftRates") || {};
                                form.setValue("paymentConfig.shiftRates", {
                                  ...currentShiftRates,
                                  [shift.id]: parseInt(e.target.value) || 0
                                });
                              }}
                              className="pl-8 h-7 w-20 text-xs text-right"
                            />
                          </div>
                        </div>
                      </div>
                    ))}

                    {shifts.length === 0 && (
                      <div className="text-center py-4 text-sm text-muted-foreground">
                        No shifts available. Create shifts first to set rates.
                      </div>
                    )}
                  </div>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Weekly Schedule Assignments - Compact Grid */}
          {shifts && shifts.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Weekly Schedule Assignments</h3>
              
              {/* Compact header row */}
              <div className="grid grid-cols-8 gap-2 text-sm font-medium text-muted-foreground border-b pb-2">
                <div className="text-left">Shifts</div>
                <div className="text-center">Mon</div>
                <div className="text-center">Tue</div>
                <div className="text-center">Wed</div>
                <div className="text-center">Thu</div>
                <div className="text-center">Fri</div>
                <div className="text-center">Sat</div>
                <div className="text-center">Sun</div>
              </div>

              {/* Compact shift rows */}
              <div className="space-y-2">
                {shifts.map((shift: any) => (
                  <div key={shift.id} className="grid grid-cols-8 gap-2 items-center py-1 hover:bg-muted/50 rounded">
                    {/* Shift info - compact */}
                    <div className="text-sm">
                      <div className="font-medium text-xs">{shift.name}</div>
                      <div className="text-xs text-muted-foreground flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {shift.startTime}-{shift.endTime}
                      </div>
                    </div>

                    {/* Day checkboxes - compact */}
                    {['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'].map((day) => (
                      <div key={day} className="flex justify-center">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Checkbox
                                id={`shift-${shift.id}-${day}`}
                                checked={watchedSchedule[day as keyof typeof watchedSchedule].includes(shift.id)}
                                onCheckedChange={() => handleShiftToggle(day as keyof StaffFormValues['weeklySchedule'], shift.id)}
                                className="h-4 w-4"
                              />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p className="text-xs">{day.charAt(0).toUpperCase() + day.slice(1)}: {shift.name}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Form Actions - Compact */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                "Create Staff"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

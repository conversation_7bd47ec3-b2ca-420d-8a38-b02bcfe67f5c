'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
// 🆕 Removed Card imports - not using cards anymore
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import {
  Eye
} from 'lucide-react';
// 🆕 Removed DropdownMenu imports - not using dropdowns anymore

// Import NEW payment system functions
import {
  getPaymentHistory
} from '@/lib/services/new-staff-balance-service';

// Import OLD payment system functions for hybrid display
import {
  getStaffPaymentHistory,
  PaymentSnapshot
} from '@/lib/services/staff-payment-service';

import type { PaymentSnapshotDocument } from '@/lib/db/v4/schemas/new-payment-schemas';

// 🆕 Unified payment record type for hybrid display
type UnifiedPaymentRecord = {
  id: string;
  date: string;
  type: 'NEW_SNAPSHOT' | 'OLD_PAYMENT';
  data: PaymentSnapshotDocument | PaymentSnapshot;
};

interface NewPaymentHistoryProps {
  staffId: string;
  className?: string;
  maxHeight?: string;
}

export default function NewPaymentHistory({
  staffId,
  className = '',
  maxHeight = '600px'
}: NewPaymentHistoryProps) {
  const { toast } = useToast();
  const [payments, setPayments] = useState<UnifiedPaymentRecord[]>([]);
  const [filteredPayments, setFilteredPayments] = useState<UnifiedPaymentRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (staffId) {
      loadPaymentHistory();
    }
  }, [staffId]);

  useEffect(() => {
    applyFilters();
  }, [payments, searchTerm]);

  const loadPaymentHistory = async () => {
    setLoading(true);
    try {
      // 🆕 Load both new payment snapshots and old payment records
      const [newSnapshots, oldPayments] = await Promise.all([
        getPaymentHistory(staffId),
        getStaffPaymentHistory(staffId)
      ]);

      // Convert to unified format
      const unifiedPayments: UnifiedPaymentRecord[] = [
        // New payment snapshots
        ...newSnapshots.map(snapshot => ({
          id: snapshot._id,
          date: snapshot.paymentDate,
          type: 'NEW_SNAPSHOT' as const,
          data: snapshot
        })),
        // Old payment records
        ...oldPayments.map(payment => ({
          id: payment.id,
          date: payment.date,
          type: 'OLD_PAYMENT' as const,
          data: payment
        }))
      ];

      // Sort by date (newest first)
      unifiedPayments.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      setPayments(unifiedPayments);
    } catch (error) {
      console.error('Error loading payment history:', error);
      toast({
        title: "❌ Error Loading History",
        description: "Failed to load payment history",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 🆕 Removed analytics loading - keeping it simple

  const applyFilters = () => {
    let filtered = [...payments];

    // Simple search filter only
    if (searchTerm) {
      filtered = filtered.filter(record => {
        if (record.type === 'NEW_SNAPSHOT') {
          const payment = record.data as PaymentSnapshotDocument;
          return payment.notes?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                 payment.netAmount.toString().includes(searchTerm);
        } else {
          const payment = record.data as PaymentSnapshot;
          return payment.netPaid.toString().includes(searchTerm) ||
                 payment.type.toLowerCase().includes(searchTerm.toLowerCase());
        }
      });
    }

    setFilteredPayments(filtered);
  };

  // 🆕 Removed export and clear filters functions - keeping it simple

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('DZD', 'DA');
  };

  

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Compact Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <h3 className="text-base font-semibold">Historique</h3>
          <div className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
            {filteredPayments.length}
          </div>
        </div>

        {/* Compact Search */}
        <Input
          placeholder="Rechercher..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-40 h-8 text-sm"
        />
      </div>

      {/* Compact Payment List */}
      <div
        className="space-y-0 overflow-y-auto border rounded-md"
        style={{ maxHeight }}
      >
        {loading ? (
          <div className="text-center py-6 text-sm text-muted-foreground">
            Chargement...
          </div>
        ) : filteredPayments.length === 0 ? (
          <div className="text-center py-6 text-sm text-muted-foreground">
            {payments.length === 0 ? 'Aucun paiement' : 'Aucun résultat'}
          </div>
        ) : (
          filteredPayments.map((record) => (
            <UnifiedPaymentCard key={record.id} record={record} formatCurrency={formatCurrency} />
          ))
        )}
      </div>
    </div>
  );
}

// 🆕 Unified Payment Card Component that handles both new snapshots and old payments
function UnifiedPaymentCard({
  record,
  formatCurrency
}: {
  record: UnifiedPaymentRecord;
  formatCurrency: (amount: number) => string;
}) {
  if (record.type === 'NEW_SNAPSHOT') {
    return <PaymentSnapshotCard payment={record.data as PaymentSnapshotDocument} formatCurrency={formatCurrency} />;
  } else {
    return <OldPaymentCard payment={record.data as PaymentSnapshot} formatCurrency={formatCurrency} />;
  }
}

// Payment Snapshot Card Component (for new system)
function PaymentSnapshotCard({
  payment,
  formatCurrency
}: {
  payment: PaymentSnapshotDocument;
  formatCurrency: (amount: number) => string;
}) {
  const [showDetails, setShowDetails] = useState(false);
  const getPaymentStatusColor = (netAmount: number) => {
    if (netAmount > 50000) return 'text-green-600';
    if (netAmount > 20000) return 'text-blue-600';
    if (netAmount > 0) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="py-1.5 px-2 border-b border-border/40 last:border-b-0 hover:bg-muted/30 transition-colors">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className="text-xs text-muted-foreground font-mono w-12 shrink-0">
            {new Date(payment.paymentDate).toLocaleDateString('fr-CA', {
              month: 'numeric',
              day: 'numeric',
            })}
          </div>
          <div className={`text-sm font-semibold ${getPaymentStatusColor(payment.netAmount)} truncate`}>
            {formatCurrency(payment.netAmount)}
          </div>
          {payment.paymentType === 'SHIFT_PAYMENT' && (
            <div className="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded shrink-0">
              {payment.shiftData?.totalShifts || 0}S
            </div>
          )}
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 shrink-0"
          onClick={() => setShowDetails(!showDetails)}
        >
          <Eye className="h-3 w-3" />
        </Button>
      </div>

      {showDetails && (
        <>
          <Separator className="my-1" />
          <div className="grid grid-cols-3 gap-x-2 gap-y-0.5 text-xs">
            {payment.paymentType === 'SHIFT_PAYMENT' ? (
              <>
                <div className="text-muted-foreground">Base</div>
                <div className="font-medium text-right col-span-2">{formatCurrency(payment.baseSalary)}</div>

                {payment.bonusAmount > 0 && (
                  <>
                    <div className="text-muted-foreground">Bonus</div>
                    <div className="font-medium text-right text-green-600 col-span-2">+{formatCurrency(payment.bonusAmount)}</div>
                  </>
                )}

                {payment.deductionAmount > 0 && (
                  <>
                    <div className="text-muted-foreground">Déduction</div>
                    <div className="font-medium text-right text-red-600 col-span-2">-{formatCurrency(payment.deductionAmount)}</div>
                  </>
                )}

                {payment.advanceAmount > 0 && (
                  <>
                    <div className="text-muted-foreground">Avance</div>
                    <div className="font-medium text-right text-orange-600 col-span-2">-{formatCurrency(payment.advanceAmount)}</div>
                  </>
                )}

                {payment.shiftData && payment.shiftData.shiftBreakdown.length > 0 && (
                  <>
                    <div className="col-span-3 mt-1 pt-1 border-t text-xs font-medium">Détail shifts</div>
                    {payment.shiftData.shiftBreakdown.map((shift) => (
                      <React.Fragment key={shift.shiftName}>
                        <div className="text-muted-foreground">{shift.shiftName}</div>
                        <div className="text-muted-foreground text-center">×{shift.count}</div>
                        <div className="font-medium text-right">{formatCurrency(shift.amount)}</div>
                      </React.Fragment>
                    ))}
                  </>
                )}
              </>
            ) : (
              <>
                <div className="text-muted-foreground">Brut</div>
                <div className="font-medium text-right col-span-2">{formatCurrency(payment.grossAmount)}</div>

                {payment.totalDeductions > 0 && (
                  <>
                    <div className="text-muted-foreground">Déductions</div>
                    <div className="font-medium text-right text-red-600 col-span-2">-{formatCurrency(payment.totalDeductions)}</div>
                  </>
                )}
              </>
            )}

            {payment.notes && (
              <>
                <div className="col-span-3 mt-1 pt-1 border-t text-xs font-medium">Note</div>
                <div className="col-span-3 text-muted-foreground text-xs">{payment.notes}</div>
              </>
            )}
          </div>
        </>
      )}
    </div>
  );
}

// Old Payment Card Component (for legacy system)
function OldPaymentCard({
  payment,
  formatCurrency
}: {
  payment: PaymentSnapshot;
  formatCurrency: (amount: number) => string;
}) {
  const [showDetails, setShowDetails] = useState(false);
  const getPaymentStatusColor = (netAmount: number) => {
    if (netAmount > 50000) return 'text-green-600';
    if (netAmount > 20000) return 'text-blue-600';
    if (netAmount > 0) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="py-1.5 px-2 border-b border-border/40 last:border-b-0 hover:bg-muted/30 transition-colors">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className="text-xs text-muted-foreground font-mono w-12 shrink-0">
            {new Date(payment.date).toLocaleDateString('fr-CA', {
              month: 'numeric',
              day: 'numeric',
            })}
          </div>
          <div className={`text-sm font-semibold ${getPaymentStatusColor(payment.netPaid)} truncate`}>
            {formatCurrency(payment.netPaid)}
          </div>
          <div className="text-xs text-muted-foreground bg-gray-100 px-1.5 py-0.5 rounded shrink-0">
            Legacy
          </div>
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 shrink-0"
          onClick={() => setShowDetails(!showDetails)}
        >
          <Eye className="h-3 w-3" />
        </Button>
      </div>

      {showDetails && (
        <>
          <Separator className="my-1" />
          <div className="grid grid-cols-3 gap-x-2 gap-y-0.5 text-xs">
            <div className="text-muted-foreground">Type</div>
            <div className="font-medium text-right col-span-2">{payment.type}</div>

            <div className="text-muted-foreground">Base</div>
            <div className="font-medium text-right col-span-2">{formatCurrency(payment.base)}</div>

            {payment.bonus > 0 && (
              <>
                <div className="text-muted-foreground">Bonus</div>
                <div className="font-medium text-right text-green-600 col-span-2">+{formatCurrency(payment.bonus)}</div>
              </>
            )}

            {payment.deduction > 0 && (
              <>
                <div className="text-muted-foreground">Déduction</div>
                <div className="font-medium text-right text-red-600 col-span-2">-{formatCurrency(payment.deduction)}</div>
              </>
            )}
          </div>
        </>
      )}
    </div>
  );
}

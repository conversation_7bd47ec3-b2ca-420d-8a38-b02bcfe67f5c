'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
// 🆕 Removed Card imports - not using cards anymore
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import {
  Eye
} from 'lucide-react';
// 🆕 Removed DropdownMenu imports - not using dropdowns anymore

// Import NEW payment system functions
import {
  getPaymentHistory
} from '@/lib/services/new-staff-balance-service';

// Import OLD payment system functions for hybrid display
import {
  getStaffPaymentHistory,
  PaymentSnapshot
} from '@/lib/services/staff-payment-service';

import type { PaymentSnapshotDocument } from '@/lib/db/v4/schemas/new-payment-schemas';

// 🆕 Unified payment record type for hybrid display
type UnifiedPaymentRecord = {
  id: string;
  date: string;
  type: 'NEW_SNAPSHOT' | 'OLD_PAYMENT';
  data: PaymentSnapshotDocument | PaymentSnapshot;
};

interface NewPaymentHistoryProps {
  staffId: string;
  className?: string;
  maxHeight?: string;
}

export default function NewPaymentHistory({
  staffId,
  className = '',
  maxHeight = '600px'
}: NewPaymentHistoryProps) {
  const { toast } = useToast();
  const [payments, setPayments] = useState<UnifiedPaymentRecord[]>([]);
  const [filteredPayments, setFilteredPayments] = useState<UnifiedPaymentRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (staffId) {
      loadPaymentHistory();
    }
  }, [staffId]);

  useEffect(() => {
    applyFilters();
  }, [payments, searchTerm]);

  const loadPaymentHistory = async () => {
    setLoading(true);
    try {
      // 🆕 Load both new payment snapshots and old payment records
      const [newSnapshots, oldPayments] = await Promise.all([
        getPaymentHistory(staffId),
        getStaffPaymentHistory(staffId)
      ]);

      // Convert to unified format
      const unifiedPayments: UnifiedPaymentRecord[] = [
        // New payment snapshots
        ...newSnapshots.map(snapshot => ({
          id: snapshot._id,
          date: snapshot.paymentDate,
          type: 'NEW_SNAPSHOT' as const,
          data: snapshot
        })),
        // Old payment records
        ...oldPayments.map(payment => ({
          id: payment.id,
          date: payment.date,
          type: 'OLD_PAYMENT' as const,
          data: payment
        }))
      ];

      // Sort by date (newest first)
      unifiedPayments.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

      setPayments(unifiedPayments);
    } catch (error) {
      console.error('Error loading payment history:', error);
      toast({
        title: "❌ Error Loading History",
        description: "Failed to load payment history",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // 🆕 Removed analytics loading - keeping it simple

  const applyFilters = () => {
    let filtered = [...payments];

    // Simple search filter only
    if (searchTerm) {
      filtered = filtered.filter(record => {
        if (record.type === 'NEW_SNAPSHOT') {
          const payment = record.data as PaymentSnapshotDocument;
          return payment.notes?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                 payment.netAmount.toString().includes(searchTerm);
        } else {
          const payment = record.data as PaymentSnapshot;
          return payment.netPaid.toString().includes(searchTerm) ||
                 payment.type.toLowerCase().includes(searchTerm.toLowerCase());
        }
      });
    }

    setFilteredPayments(filtered);
  };

  // 🆕 Removed export and clear filters functions - keeping it simple

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-DZ', {
      style: 'currency',
      currency: 'DZD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount).replace('DZD', 'DA');
  };

  

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 🆕 Removed analytics summary - keeping it simple */}

      {/* 🆕 Simplified Header */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          {filteredPayments.length} payment{filteredPayments.length !== 1 ? 's' : ''}
        </div>

        {/* Simple search only */}
        <div className="w-64">
          <Input
            placeholder="Rechercher..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="h-8"
          />
        </div>
      </div>

      {/* 🆕 Removed complex filters - keeping it simple */}

      {/* Payment List */}
      <div 
        className="space-y-0.5 overflow-y-auto"
        style={{ maxHeight }}
      >
        {loading ? (
          <div className="text-center py-8 text-muted-foreground">
            Loading payment history...
          </div>
        ) : filteredPayments.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            {payments.length === 0 ? 'No payments found' : 'No payments match your filters'}
          </div>
        ) : (
          filteredPayments.map((record) => (
            <UnifiedPaymentCard key={record.id} record={record} formatCurrency={formatCurrency} />
          ))
        )}
      </div>
    </div>
  );
}

// 🆕 Unified Payment Card Component that handles both new snapshots and old payments
function UnifiedPaymentCard({
  record,
  formatCurrency
}: {
  record: UnifiedPaymentRecord;
  formatCurrency: (amount: number) => string;
}) {
  if (record.type === 'NEW_SNAPSHOT') {
    return <PaymentSnapshotCard payment={record.data as PaymentSnapshotDocument} formatCurrency={formatCurrency} />;
  } else {
    return <OldPaymentCard payment={record.data as PaymentSnapshot} formatCurrency={formatCurrency} />;
  }
}

// Payment Snapshot Card Component (for new system)
function PaymentSnapshotCard({
  payment,
  formatCurrency
}: {
  payment: PaymentSnapshotDocument;
  formatCurrency: (amount: number) => string;
}) {
  const [showDetails, setShowDetails] = useState(false);
  const getPaymentStatusColor = (netAmount: number) => {
    if (netAmount > 50000) return 'bg-green-100 text-green-800 border-green-200';
    if (netAmount > 20000) return 'bg-blue-100 text-blue-800 border-blue-200';
    if (netAmount > 0) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <div className="py-2 px-3 border-b border-border/60 last:border-b-0">
      <div className="flex items-center justify-between mb-1">
        <div className="flex items-center gap-2">
          <div className="text-xs text-muted-foreground">
            {new Date(payment.paymentDate).toLocaleDateString('fr-CA', {
              month: 'short',
              day: 'numeric',
            })}
          </div>
          <Badge className={`text-xs font-bold ${getPaymentStatusColor(payment.netAmount)}`}>
            {formatCurrency(payment.netAmount)}
          </Badge>
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="h-4 w-4"
          onClick={() => setShowDetails(!showDetails)}
        >
          <Eye className="h-3 w-3" />
        </Button>
      </div>

      {(showDetails || payment.paymentType === 'SHIFT_PAYMENT') && (
        <>
          <Separator className="my-1" />
          {payment.paymentType === 'SHIFT_PAYMENT' ? (
            <div className="grid grid-cols-2 gap-x-2 gap-y-0.5 text-xs">
              <div className="text-muted-foreground">Base</div>
              <div className="font-medium text-right">{formatCurrency(payment.baseSalary)}</div>
              
              <div className="text-muted-foreground">Bonus</div>
              <div className="font-medium text-right text-green-600">+{formatCurrency(payment.bonusAmount)}</div>

              <div className="text-muted-foreground">Deduction</div>
              <div className="font-medium text-right text-red-600">-{formatCurrency(payment.deductionAmount)}</div>

              <div className="text-muted-foreground">Advance</div>
              <div className="font-medium text-right text-orange-600">-{formatCurrency(payment.advanceAmount)}</div>

              {payment.shiftData && (
                <>
                  <div className="col-span-2 mt-1 pt-1 border-t font-medium">Shifts: {payment.shiftData.totalShifts}</div>
                  {payment.shiftData.shiftBreakdown.map((shift) => (
                    <div key={shift.shiftName}>
                      <div className="pl-2 text-muted-foreground">{shift.shiftName} x {shift.count}</div>
                      <div className="font-medium text-right">{formatCurrency(shift.amount)}</div>
                    </div>
                  ))}
                </>
              )}

              {payment.notes && (
                  <>
                      <div className="col-span-2 mt-1 pt-1 border-t font-medium">Notes</div>
                      <div className="col-span-2 text-muted-foreground text-xs">{payment.notes}</div>
                  </>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-x-2 gap-y-0.5 text-xs">
              <div className="text-muted-foreground">Gross</div>
              <div className="font-medium text-right">{formatCurrency(payment.grossAmount)}</div>

              <div className="text-muted-foreground">Total Deductions</div>
              <div className="font-medium text-right text-red-600">-{formatCurrency(payment.totalDeductions)}</div>

              {payment.notes && (
                  <>
                      <div className="col-span-2 mt-1 pt-1 border-t font-medium">Notes</div>
                      <div className="col-span-2 text-muted-foreground text-xs">{payment.notes}</div>
                  </>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
}

// Old Payment Card Component (for legacy system)
function OldPaymentCard({
  payment,
  formatCurrency
}: {
  payment: PaymentSnapshot;
  formatCurrency: (amount: number) => string;
}) {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <div className="py-2 px-3 border-b border-border/60 last:border-b-0">
      <div className="flex items-center justify-between mb-1">
        <div className="flex items-center gap-2">
          <div className="text-xs text-muted-foreground">
            {new Date(payment.date).toLocaleDateString('fr-CA', {
              month: 'short',
              day: 'numeric',
            })}
          </div>
          <Badge className={`text-xs font-bold ${getPaymentStatusColor(payment.netPaid)}`}>
            {formatCurrency(payment.netPaid)}
          </Badge>
          <Badge className="bg-gray-200 text-gray-600 text-xs px-1.5 py-0.5">
            Legacy
          </Badge>
        </div>
        <Button
          variant="ghost"
          size="icon"
          className="h-4 w-4"
          onClick={() => setShowDetails(!showDetails)}
        >
          <Eye className="h-3 w-3" />
        </Button>
      </div>

      {showDetails && (
        <>
          <Separator className="my-1" />
          <div className="grid grid-cols-2 gap-x-2 gap-y-0.5 text-xs">
            <div className="text-muted-foreground">Type</div>
            <div className="font-medium text-right">{payment.type}</div>

            <div className="text-muted-foreground">Base</div>
            <div className="font-medium text-right">{formatCurrency(payment.base)}</div>

            <div className="text-muted-foreground">Bonus</div>
            <div className="font-medium text-right text-green-600">+{formatCurrency(payment.bonus)}</div>

            <div className="text-muted-foreground">Deduction</div>
            <div className="font-medium text-right text-red-600">-{formatCurrency(payment.deduction)}</div>
          </div>
        </>
      )}
    </div>
  );
}
